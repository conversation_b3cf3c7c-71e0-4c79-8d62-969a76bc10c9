import React from "react";

const Navbar = () => {
  return (
    <div>
      {/* Debug section - temporary */}
      <div className="p-4 bg-red-500 text-white text-center">
        <p>Tailwind Test: If this is red with white text, basic Tailwind is working</p>
        <div className="mt-2 flex gap-2 justify-center">
          <div className="w-4 h-4 bg-blue-500"></div>
          <div className="w-4 h-4 bg-green-500"></div>
          <div className="w-4 h-4 bg-yellow-500"></div>
        </div>
      </div>

      <nav className="w-full py-4 px-6 flex justify-between items-center sticky top-0 z-50 bg-gray-800 shadow-md">
      {/* Logo */}
      <a
        className="text-white text-2xl font-extrabold tracking-wide no-underline hover:text-gray-300 transition-colors duration-300"
        href="#"
      >
        DevFolio
      </a>

      {/* Menu Items */}
      <ul className="flex list-none justify-end items-center gap-6 text-white font-medium">
        <li className="hover:text-gray-300 cursor-pointer transition-colors duration-300">
          Hi, Daniyal
        </li>
        <li className="hover:text-red-400 cursor-pointer transition-colors duration-300">
          Logout
        </li>
        <li className="hover:text-green-400 cursor-pointer transition-colors duration-300">
          Login
        </li>
        <li className="hover:text-blue-400 cursor-pointer transition-colors duration-300">
          Registration
        </li>
        <li className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white cursor-pointer transition-all duration-300">
          Create Post
        </li>
      </ul>
    </nav>
    </div>
  );
};

export default Navbar;
